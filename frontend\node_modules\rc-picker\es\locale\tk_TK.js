import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
import { commonLocale } from "./common";
var locale = _objectSpread(_objectSpread({}, commonLocale), {}, {
  locale: 'tk_TK',
  today: '<PERSON><PERSON>ü<PERSON>',
  now: '<PERSON><PERSON>wa<PERSON>',
  backToToday: '<PERSON><PERSON><PERSON><PERSON> gaýt',
  ok: 'Bol<PERSON>ar',
  clear: 'Arassala',
  month: 'Aý',
  week: 'Hep<PERSON>',
  year: 'Ýyl',
  timeSelect: 'Wagt saýla',
  dateSelect: 'Gün saýla',
  monthSelect: 'Aý saýla',
  yearSelect: 'Ýyl saýla',
  decadeSelect: 'On ýyllygy saýla',
  dateFormat: 'D/M/YYYY',
  dateTimeFormat: 'D/M/YYYY HH:mm:ss',
  previousMonth: '<PERSON>ň<PERSON> aý (PageUp)',
  nextMonth: 'Soňky aý (PageDown)',
  previousYear: 'Öňki ýyl (Control + çep)',
  nextYear: 'Soňky ýyl (Control + sag)',
  previousDecade: '<PERSON><PERSON><PERSON> on ýyl',
  nextDecade: 'Soňky on ýyl',
  previousCentury: '<PERSON>ň<PERSON> asyr',
  nextCentury: 'Soňky asyr'
});
export default locale;