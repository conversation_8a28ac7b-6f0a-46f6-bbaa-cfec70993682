import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
import { commonLocale } from "./common";
var locale = _objectSpread(_objectSpread({}, commonLocale), {}, {
  locale: 'ms_MY',
  today: 'Hari ini',
  now: '<PERSON>kara<PERSON>',
  backToToday: 'Ke<PERSON>li ke hari ini',
  ok: 'OK',
  timeSelect: '<PERSON>lih masa',
  dateSelect: 'Pilih tarikh',
  weekSelect: 'Pilih minggu',
  clear: 'Padam',
  week: '<PERSON>gu',
  month: 'Bulan',
  year: 'Tahun',
  previousMonth: 'Bulan lepas',
  nextMonth: 'Bulan depan',
  monthSelect: '<PERSON>lih bulan',
  yearSelect: '<PERSON>lih tahun',
  decadeSelect: 'Pilih dekad',
  dateFormat: 'M/D/YYYY',
  dateTimeFormat: 'M/D/YYYY HH:mm:ss',
  previousYear: '<PERSON><PERSON> lepas (Ctrl+left)',
  nextYear: '<PERSON>hun depan (Ctrl+right)',
  previousDecade: 'Dekad lepas',
  nextDecade: 'Dekad depan',
  previousCentury: '<PERSON>bad lepas',
  nextCentury: 'Abad depan',
  monthBeforeYear: false
});
export default locale;