import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
import { commonLocale } from "./common";
var locale = _objectSpread(_objectSpread({}, commonLocale), {}, {
  locale: 'tr_TR',
  today: 'Bugün',
  now: '<PERSON>im<PERSON>',
  backToToday: '<PERSON><PERSON><PERSON><PERSON>',
  ok: 'Tamam',
  clear: 'Temizle',
  week: 'Hafta',
  month: 'Ay',
  year: 'Yıl',
  timeSelect: '<PERSON>aman Seç',
  dateSelect: '<PERSON><PERSON><PERSON>',
  monthSelect: 'Ay Seç',
  yearSelect: 'Yıl Seç',
  decadeSelect: 'On Yıl Seç',
  dateFormat: 'DD/MM/YYYY',
  dateTimeFormat: 'DD/MM/YYYY HH:mm:ss',
  previousMonth: 'Önceki Ay (PageUp)',
  nextMonth: '<PERSON><PERSON><PERSON> (PageDown)',
  previousYear: '<PERSON>nce<PERSON>ıl (Control + Sol)',
  nextYear: '<PERSON>rak<PERSON>ıl (Control + Sağ)',
  previousDecade: 'Önceki On Yıl',
  nextDecade: 'Sonraki On Yıl',
  previousCentury: 'Önceki Yüzyıl',
  nextCentury: 'Sonraki Yüzyıl',
  shortWeekDays: ['Paz', 'Pzt', 'Sal', 'Çar', 'Per', 'Cum', 'Cmt'],
  shortMonths: ['Oca', 'Şub', 'Mar', 'Nis', 'May', 'Haz', 'Tem', 'Ağu', 'Eyl', 'Eki', 'Kas', 'Ara']
});
export default locale;