import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
import { commonLocale } from "./common";
var locale = _objectSpread(_objectSpread({}, commonLocale), {}, {
  locale: 'ku',
  today: 'Îro',
  now: '<PERSON>ha',
  backToToday: '<PERSON><PERSON><PERSON> îro',
  ok: 'Tema<PERSON>',
  clear: 'Paqij bike',
  week: 'Sêb<PERSON>',
  month: 'Meh',
  year: 'Sal',
  timeSelect: 'Dem<PERSON> hilbijêre',
  dateSelect: '<PERSON><PERSON><PERSON> hilbijêre',
  monthSelect: 'Meh hilbijêre',
  yearSelect: '<PERSON> hilbijêre',
  decadeSelect: 'Dehsal hilbijêre',
  dateFormat: 'D/M/YYYY',
  dateTimeFormat: 'D/M/YYYY HH:mm:ss',
  previousMonth: '<PERSON><PERSON> peş (PageUp))',
  nextMonth: '<PERSON><PERSON> paş (PageDown)',
  previousYear: '<PERSON><PERSON> peş (Control + şep)',
  nextYear: '<PERSON><PERSON> paş (Control + rast)',
  previousDecade: 'De<PERSON>ale<PERSON> peş',
  nextDecade: 'De<PERSON>alen paş',
  previousCentury: 'Sedsalen peş',
  nextCentury: 'Sedsalen paş'
});
export default locale;