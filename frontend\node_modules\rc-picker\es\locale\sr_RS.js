import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
import { commonLocale } from "./common";
var locale = _objectSpread(_objectSpread({}, commonLocale), {}, {
  locale: 'sr_RS',
  today: '<PERSON><PERSON>',
  now: 'Sada',
  backToToday: 'Vrati se na danas',
  ok: 'U redu',
  clear: '<PERSON><PERSON><PERSON><PERSON>',
  week: '<PERSON><PERSON><PERSON>',
  month: 'Mesec',
  year: '<PERSON><PERSON>',
  timeSelect: 'Izaberi vreme',
  dateSelect: 'Izaberi datum',
  monthSelect: 'Izaberi mesec',
  yearSelect: 'Izaberi godinu',
  decadeSelect: 'Izaberi deceniju',
  dateFormat: 'DD.MM.YYYY',
  dateTimeFormat: 'DD.MM.YYYY HH:mm:ss',
  previousMonth: 'Prethodni mesec (PageUp)',
  nextMonth: '<PERSON><PERSON><PERSON><PERSON><PERSON> mesec (PageDown)',
  previousYear: 'Preth<PERSON>na godina (Control + left)',
  nextYear: '<PERSON><PERSON><PERSON><PERSON><PERSON> godina (Control + right)',
  previousDecade: 'Prethodna decenija',
  nextDecade: 'Sledeća decenija',
  previousCentury: 'Prethodni vek',
  nextCentury: 'Sledeći vek'
});
export default locale;