import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
import { commonLocale } from "./common";
var locale = _objectSpread(_objectSpread({}, commonLocale), {}, {
  locale: 'sl_SI',
  today: 'Dane<PERSON>',
  now: 'Trenutno',
  backToToday: 'Na<PERSON><PERSON> na danes',
  ok: 'V redu',
  clear: '<PERSON><PERSON><PERSON><PERSON>',
  week: 'Teden',
  month: 'Mesec',
  year: 'Leto',
  timeSelect: 'Izberite čas',
  dateSelect: 'Izberite datum',
  monthSelect: 'Izberite mesec',
  yearSelect: 'Izberite leto',
  decadeSelect: 'Izberite desetletje',
  dateFormat: 'DD.MM.YYYY',
  dateTimeFormat: 'DD.MM.YYYY HH:mm:ss',
  previousMonth: 'Prejšnji mesec (PageUp)',
  nextMonth: 'Nasle<PERSON><PERSON> mesec (PageDown)',
  previousYear: 'Prej<PERSON>n<PERSON> leto (Control + left)',
  nextYear: 'Naslednje leto (Control + right)',
  previousDecade: 'Prejšnje desetletje',
  nextDecade: 'Naslednje desetletje',
  previousCentury: 'Prej<PERSON>nje stoletje',
  nextCentury: 'Naslednje stoletje'
});
export default locale;