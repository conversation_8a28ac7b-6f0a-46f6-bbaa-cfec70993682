import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
import { commonLocale } from "./common";
var locale = _objectSpread(_objectSpread({}, commonLocale), {}, {
  locale: 'lt_LT',
  today: 'Šiandien',
  now: '<PERSON><PERSON>',
  backToToday: '<PERSON><PERSON><PERSON> šiandien',
  ok: 'Gerai',
  clear: 'Išvalyti',
  week: 'Savait<PERSON>',
  month: 'Mė<PERSON>is',
  year: 'Metai',
  timeSelect: 'Pasirinkti laiką',
  dateSelect: 'Pasirinkti datą',
  weekSelect: 'Pasirinkti savaitę',
  monthSelect: 'Pasirinkti mėnesį',
  yearSelect: 'Pasirinkti metus',
  decadeSelect: 'Pasirinkti dešimtmetį',
  dateFormat: 'YYYY-MM-DD',
  dayFormat: 'DD',
  dateTimeFormat: 'YYYY-MM-DD HH:MM:SS',
  previousMonth: '<PERSON>uvę<PERSON> mėnes<PERSON> (PageUp)',
  nextMonth: '<PERSON>as mėnesis (PageDown)',
  previousYear: '<PERSON>u<PERSON><PERSON> metai (Control + left)',
  nextYear: '<PERSON>i metai (Control + right)',
  previousDecade: 'Buvę<PERSON> dešimtmetis',
  nextDecade: 'Kitas dešimtmetis',
  previousCentury: 'Buvęs amžius',
  nextCentury: 'Kitas amžius'
});
export default locale;