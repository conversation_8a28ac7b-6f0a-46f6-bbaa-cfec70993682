import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
import { commonLocale } from "./common";
var locale = _objectSpread(_objectSpread({}, commonLocale), {}, {
  locale: 'ug_CN',
  today: 'بۈگۈن',
  now: 'ھازىر',
  backToToday: 'بۈگۈنگە قايتىش',
  ok: 'مۇقىملاشتۇرۇش',
  timeSelect: 'ۋاقىت تاللاش',
  dateSelect: 'كۈن تاللاش',
  clear: 'تازىلاش',
  week: 'ھەپتە',
  month: 'ئاي',
  year: 'يىل',
  previousMonth: 'ئالدىنقى ئاي(ئالدىنقى بەت )',
  nextMonth: 'كېلەركى ئاي (كېلەركى بەت)',
  monthSelect: 'ئاي تاللاش',
  yearSelect: 'يىل تاللاش',
  decadeSelect: 'يىللارنى تاللاش',
  yearFormat: 'YYYY-يىلى',
  dayFormat: 'D-كۈنى',
  dateFormat: 'YYYY-يىلىM-ئاينىڭD-كۈنى',
  dateTimeFormat: 'YYYY-يىلىM—ئاينىڭD-كۈنى، HH:mm:ss',
  previousYear: 'ئالدىنقى يىلى (Controlبىلەن يۆنىلىش كونۇپكىسى)',
  nextYear: 'كېلەركى يىلى (Controlبىلەن يۆنىلىش كونۇپكىسى)',
  previousDecade: 'ئالدىنقى يىللار',
  nextDecade: 'كېيىنكى يىللار',
  previousCentury: 'ئالدىنقى ئەسىر',
  nextCentury: 'كېيىنكى ئەسىر',
  monthBeforeYear: false
});
export default locale;