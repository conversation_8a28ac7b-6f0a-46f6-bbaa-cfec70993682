import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
import { commonLocale } from "./common";
var locale = _objectSpread(_objectSpread({}, commonLocale), {}, {
  locale: 'lv_LV',
  today: 'Š<PERSON><PERSON>',
  now: 'Tagad',
  backToToday: 'Atpakaļ pie šodienas',
  ok: 'OK',
  clear: 'Skaidrs',
  week: 'Nedēļa',
  month: 'Mēnes<PERSON>',
  year: 'Gads',
  timeSelect: 'Izvēlieties laiku',
  dateSelect: 'Izvēlieties datumu',
  monthSelect: 'Izvēlieties mēnesi',
  yearSelect: 'Izvēlieties gadu',
  decadeSelect: 'Izvēlieties desmit gadus',
  dateFormat: 'D.M.YYYY',
  dateTimeFormat: 'D.M.YYYY HH:mm:ss',
  previousMonth: 'Iepriek<PERSON><PERSON><PERSON><PERSON> mēnesis (PageUp)',
  nextMonth: '<PERSON><PERSON><PERSON><PERSON>ēnes (PageDown)',
  previousYear: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gads (Control + left)',
  nextYear: 'Nākamgad (Control + right)',
  previousDecade: 'Pēdēj<PERSON> desmitgadē',
  nextDecade: 'Nākamā desmitgade',
  previousCentury: 'Pagājušajā gadsimtā',
  nextCentury: 'Nākamajā gadsimtā'
});
export default locale;